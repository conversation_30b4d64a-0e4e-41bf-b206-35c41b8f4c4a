import { ADVFNFormData, ADVFNFormData as ADVFNFormSchema } from '@quarterback/types';
import { useCallback } from 'react';
import { useFormValidation } from '@/hooks/useFormValidation';
import { TextField } from '@/components/forms/fields/TextField';
import { TextArea } from '@/components/forms/fields/TextArea';

interface ADVFNFormProps {
    formData: ADVFNFormData;
    setFormData: (data: ADVFNFormData) => void;
}

export default function ADVFNForm({ formData, setFormData }: ADVFNFormProps) {
    const { getFieldError, validateField } = useFormValidation(ADVFNFormSchema, formData);

    const updateField = useCallback(
        (field: keyof ADVFNFormData, value: string | undefined) => {
            const updatedData = {
                ...formData,
                [field]: value
            };

            setFormData(updatedData);
            validateField(field);
        },
        [formData, setFormData, validateField]
    );

    const handleBlur = useCallback(
        (field: keyof ADVFNFormData) => {
            validateField(field);
        },
        [validateField]
    );

    return (
        <div className="space-y-4 mt-4">
            <TextField
                value={formData.title || ''}
                onChange={(value) => updateField('title', value)}
                onBlur={() => handleBlur('title')}
                placeholder="Post Title"
                error={getFieldError('title')}
                styled
                textSize="lg"
            />

            <TextArea
                value={formData.body || ''}
                onChange={(value) => updateField('body', value)}
                onBlur={() => handleBlur('body')}
                required
                error={getFieldError('body')}
                styled
                placeholder="Write some post content here. This is a paragraph to be filled with some content about the post. This should work as expected. Lorem"
                textSize="sm"
            />

            <TextField
                label="URL"
                value={formData.url || ''}
                onChange={(value) => updateField('url', value)}
                onBlur={() => handleBlur('url')}
                placeholder="https://advfn.com/..."
                error={getFieldError('url')}
            />

            <TextField
                label="Image URL"
                value={formData.image || ''}
                onChange={(value) => updateField('image', value)}
                onBlur={() => handleBlur('image')}
                placeholder="https://example.com/image.jpg"
                error={getFieldError('image')}
            />

            <TextField
                label="Likes"
                value={formData.likes || ''}
                onChange={(value) => updateField('likes', value)}
                onBlur={() => handleBlur('likes')}
                placeholder="0"
                type="number"
                error={getFieldError('likes')}
            />
        </div>
    );
}
