import ActivityFormatIndicator from '@/components/ActivityFormatIndicator';
import { DateTimePicker } from '@/components/forms/fields';
import { FileUpload } from '@/components/forms/fields/FileUpload';
import { useOrganisation } from '@/components/OrganisationProvider';
import SidePane from '@/components/SidePane';
import { useFileUpload } from '@/hooks/useFileUpload';
import { useFormValidation } from '@/hooks/useFormValidation';
import { mapFormDataToActivity2 } from '@/util/activityMapper';
import {
    ActivityFile,
    ActivityFormData,
    format,
    Format,
    MediaFormData
} from '@quarterback/types';
import { log } from '@quarterback/util/gcp';
import { useCallback, useEffect, useMemo, useReducer, useState } from 'react';
import AuthorsSearchDropdown from '../components/AuthorsSearchDropdown';
import NewsSourceSearchDropdown from '../components/NewsSourceSearchDropdown';
import RenderActivityForm from '../components/RenderActivityForm';
import SocialSourceDropdown from '../components/SocialSourceDropdown';
import { formReducer, initialFormState } from '../reducers/activityFormReducer';
import Dropdown from '@/components/ui/Dropdown';

export default function ManualActivityFormFactory({
    addingActivity,
    setAddingActivity,
    onSubmit
}: {
    onSubmit: (data: any) => Promise<any>;
    addingActivity: boolean;
    setAddingActivity: (value: boolean) => void;
}) {
    const [formData, dispatch] = useReducer(formReducer, initialFormState);
    const [isSaving, setIsSaving] = useState(false);
    const organisation = useOrganisation();
    const [savingError, setSavingError] = useState<Error | undefined>(undefined);
    const [files, setFiles] = useState<File[]>([]);

    const { uploadFiles, isUploading, uploadedFiles, setUploadedFiles } = useFileUpload();

    const { validate, errors, setErrors, validateField, isValid } = useFormValidation(
        ActivityFormData,
        formData
    );

    console.log('isValid', isValid, 'errors', errors);

    useEffect(() => {
        setErrors({});
    }, [formData.format, formData.source, setErrors]);

    const formatOptions = useMemo(() => {
        return Object.values(format.Values).map((it) => ({
            value: it as Format,
            name: <ActivityFormatIndicator activity={it} />
        }));
    }, []);

    const handleFormSubmit = useCallback(async () => {
        setSavingError(undefined);
        if (validate()) {
            try {
                setIsSaving(true);

                // First, upload any files that haven't been uploaded yet
                let tempUploadedFiles: ActivityFile[] = [];
                if (files.length > 0) {
                    tempUploadedFiles = await uploadFiles(files);
                    if (tempUploadedFiles.length !== files.length) {
                        throw new Error('Some files failed to upload');
                    }
                }

                formData.files = tempUploadedFiles;

                // Create the activity
                const activity2Data = mapFormDataToActivity2(formData, {
                    symbol: organisation.selected?.entity.symbol,
                    exchange: organisation.selected?.entity.exchange
                });

                // Submit the activity
                await onSubmit([activity2Data]);

                // Reset the form
                dispatch({ type: 'RESET' });
                setErrors({});
                setFiles([]);
                setUploadedFiles([]);
                setAddingActivity(false);
            } catch (error: any) {
                log('ERROR', 'Error submitting activity:', { error });
                setSavingError(error);
            } finally {
                setIsSaving(false);
            }
        } else {
            // Focus the first field with an error
            const firstErrorField = Object.keys(errors)[0];
            const element = document.querySelector(`[name="${firstErrorField}"]`);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                (element as HTMLElement).focus();
            }
        }
    }, [
        validate,
        formData,
        organisation.selected?.entity.symbol,
        organisation.selected?.entity.exchange,
        onSubmit,
        setErrors,
        setAddingActivity,
        errors,
        files,
        uploadFiles,
        setUploadedFiles,
        setFiles
    ]);

    // Handle format change
    const handleFormatChange = useCallback((value: string) => {
        dispatch({
            type: 'SET_FORMAT',
            payload: value as Format
        });
    }, []);

    const handleDateChange = useCallback(
        (date: Date) => {
            if (!isSaving) {
                dispatch({
                    type: 'UPDATE_FIELD',
                    payload: {
                        field: 'posted',
                        value: date.toISOString()
                    }
                });
                validateField('posted');
            }
        },
        [isSaving, validateField]
    );

    const handleDateBlur = useCallback(() => {
        validateField('posted');
    }, [validateField]);

    return (
        <SidePane
            breadCrumbs={['Manual Activity', 'New activity']}
            open={addingActivity}
            confirmButtonText={isSaving ? 'Saving...' : 'Add activity'}
            cancelButtonText="Cancel"
            isDisabledConfirm={isSaving || isUploading}
            isDsabledCancel={isSaving || isUploading}
            onConfirm={handleFormSubmit}
            onCancel={() => {
                if (!isSaving) {
                    setAddingActivity(false);
                    dispatch({ type: 'RESET' });
                    setErrors({});
                    setSavingError(undefined);
                }
            }}>
            <div className="flex flex-col gap-3">
                {isSaving && (
                    <div className="bg-blue-50 p-3 rounded-md mb-2 flex items-center">
                        <div className="flex space-x-2 items-center text-blue-700">
                            <div className="size-1.5 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                            <div className="size-1.5 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                            <div className="size-1.5 bg-blue-600 rounded-full animate-bounce"></div>
                            <span className="ml-2 text-sm font-medium">
                                Saving activity...
                            </span>
                        </div>
                    </div>
                )}

                {savingError && (
                    <div className="bg-red-50 p-3 rounded-md mb-2 flex items-center">
                        <div className="flex space-x-2 items-center text-red-700">
                            <span className="ml-2 text-sm font-medium">
                                Error saving activity: {savingError.message}
                            </span>
                        </div>
                    </div>
                )}

                <div className={isSaving ? 'pointer-events-none opacity-70' : ''}>
                    <Dropdown
                        options={formatOptions}
                        selected={formData.format}
                        onChange={handleFormatChange}
                        placeholder="Format"
                        borderDashed
                        styled
                    />

                    {formData.format === 'media' ? (
                        <div className="space-y-4 mt-4">
                            <NewsSourceSearchDropdown
                                key={`source-${formData.format}`}
                                value={(formData as MediaFormData)?.newsSource?.url || ''}
                                dispatch={isSaving ? () => {} : dispatch}
                                error={errors['source']}
                            />
                        </div>
                    ) : null}

                    {['chatter', 'broadcast'].includes(formData.format) ? (
                        <div className="space-y-4 mt-4">
                            <SocialSourceDropdown
                                key={`source-${formData.format}`}
                                value={formData.source}
                                dispatch={isSaving ? () => {} : dispatch}
                                error={errors['source']}
                            />
                        </div>
                    ) : null}

                    {!['media', 'announcement'].includes(formData.format) ? (
                        <div className="space-y-4 mt-4">
                            <AuthorsSearchDropdown
                                key={`author-${formData.format}-${formData.source}`}
                                value={formData.author?.key || ''}
                                dispatch={isSaving ? () => {} : dispatch}
                                error={errors['author']}
                                sourceFilter={formData.source}
                            />
                        </div>
                    ) : null}

                    <div className="space-y-4 mt-4">
                        <DateTimePicker
                            label="Posted Date"
                            value={formData.posted ? new Date(formData.posted) : null}
                            onChange={handleDateChange}
                            onBlur={handleDateBlur}
                            error={errors['posted']}
                            required={true}
                            timezone="Australia/Sydney"
                        />
                    </div>

                    <RenderActivityForm
                        formData={formData}
                        dispatch={isSaving ? () => {} : dispatch}
                    />

                    <div className="mt-4">
                        <label className="block text-sm font-medium text-gray-700">
                            Attachments
                        </label>
                        <FileUpload
                            files={files}
                            setFiles={setFiles}
                            uploadedFiles={uploadedFiles}
                            maxSize={10}
                            multiple={true}
                        />
                    </div>
                </div>
            </div>
        </SidePane>
    );
}
