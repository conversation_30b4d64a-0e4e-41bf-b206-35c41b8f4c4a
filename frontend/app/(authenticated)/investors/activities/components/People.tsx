import { Activity } from '@quarterback/types';
import React, { useCallback, useMemo } from 'react';
import ActivitySourceIcon from '@/components/ActivitySourceIcon';
import {
    sentimentScore,
    DiscreteSentiment,
    discreteSentiment,
    SENTIMENT_TEXT_COLORS
} from '@/util/sentiment';
import hasField from '@/util/hasField';
import { author as getAuthor } from '@/util/author';
import { usePeopleLayout } from '../../people/PeopleLayoutContext';
import { useRouter } from 'next/navigation';
import { useInvestorsLayout } from '../../InvestorsLayoutContext';

interface AuthorStats {
    key: string;
    author: string;
    postCount: number;
    averageSentiment: number;
    activities: Activity[];
    sampleActivity: Activity; // For getting the source icon
}

export default function People({ activities }: { activities: Array<Activity> }) {
    const router = useRouter();
    const { setIsRightPanelExpanded } = useInvestorsLayout();

    const handleClick = useCallback(
        (key: string) => {
            setIsRightPanelExpanded(true);
            router.push(`/investors/people/${encodeURIComponent(key)}`);
        },
        [router, setIsRightPanelExpanded]
    );

    const authorStats = useMemo(() => {
        // Group activities by author
        const authorMap = new Map<string, Activity[]>();

        activities.forEach((activity) => {
            const authorKey: string = activity.author?.key!;

            if (authorKey) {
                if (!authorMap.has(authorKey)) {
                    authorMap.set(authorKey, []);
                }
                authorMap.get(authorKey)!.push(activity);
            }
        });

        // Calculate stats for each author
        const stats: AuthorStats[] = [];
        authorMap.forEach((authorActivities, authorKey) => {
            // Calculate average sentiment for this author
            const sentimentActivities = authorActivities.filter(hasField('sentiment'));
            let averageSentiment = 0;

            if (sentimentActivities.length > 0) {
                const totalSentiment = sentimentActivities.reduce(
                    (sum, activity) => sum + sentimentScore(activity.sentiment),
                    0
                );
                averageSentiment = totalSentiment / sentimentActivities.length;
            }

            stats.push({
                key: authorKey,
                author: getAuthor(authorActivities[0])!,
                postCount: authorActivities.length,
                averageSentiment,
                activities: authorActivities,
                sampleActivity: authorActivities[0]
            });
        });

        return stats.sort((a, b) => b.postCount - a.postCount).slice(0, 5);
    }, [activities]);

    const getSentimentColor = (sentiment: number) => {
        const discrete = discreteSentiment(sentiment);
        return SENTIMENT_TEXT_COLORS[discrete];
    };

    if (authorStats.length === 0) {
        return (
            <div className="text-center text-gray-500 py-8 text-sm">
                No author data available
            </div>
        );
    }

    return (
        <div className="overflow-hidden">
            <div className="overflow-x-auto">
                <table className="min-w-full">
                    <thead>
                        <tr className="border-b border-gray-200 bg-gray-100">
                            <th className="text-left text-xs font-normal text-gray-500 tracking-wider py-3 px-2">
                                Author
                            </th>
                            <th className="text-left text-xs font-normal text-gray-500 tracking-wider py-3 px-4">
                                Number of Posts
                            </th>
                            <th className="text-right text-xs font-normal text-gray-500 tracking-wider py-3 px-2">
                                Average Sentiment
                            </th>
                        </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                        {authorStats.map((stats) => (
                            <tr
                                className="hover:bg-gray-50 hover:cursor-pointer"
                                key={stats.key}
                                onClick={() => handleClick(stats.key)}>
                                <td className="py-3 px-2">
                                    <div className="flex items-center gap-x-2 min-w-0">
                                        <ActivitySourceIcon
                                            className="rounded-md size-5 object-cover flex-shrink-0"
                                            activity={stats.sampleActivity}
                                        />
                                        <span className="text-xs font-medium text-gray-900 truncate">
                                            {stats.author}
                                        </span>
                                    </div>
                                </td>
                                <td className="py-3 px-4">
                                    <div className="text-sm text-gray-900 line-clamp-2 break-words">
                                        {stats.postCount}
                                    </div>
                                </td>
                                <td className="py-3 px-2 text-right">
                                    <span
                                        className="text-sm font-medium text-gray-900"
                                        style={{
                                            color: getSentimentColor(
                                                stats.averageSentiment
                                            )
                                        }}>
                                        {stats.averageSentiment.toFixed(2)}
                                    </span>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
}
