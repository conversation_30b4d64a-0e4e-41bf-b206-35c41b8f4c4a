import { Activity } from '@quarterback/types';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useMemo } from 'react';
import { useInvestorsLayout } from '../../InvestorsLayoutContext';

interface ThreadStats {
    activityId: string;
    threadId: string;
    title: string;
    views: number;
    replies: number;
    sampleActivity: Activity; // For getting the first activity of the thread
}

export default function Threads({ activities }: { activities: Array<Activity> }) {
    const searchParams = useSearchParams();
    const router = useRouter();
    const { setIsRightPanelExpanded } = useInvestorsLayout();

    const threadStats = useMemo(() => {
        const hotcopperActivities = activities.filter((activity) => {
            return (
                activity.type === 'hotcopper' &&
                'thread' in activity &&
                activity.thread?.thread !== undefined
            );
        });

        // Group activities by thread ID
        const threadMap = new Map<string, Activity[]>();

        hotcopperActivities.forEach((activity) => {
            // Type assertion since we've already filtered for hotcopper activities
            const hotcopperActivity = activity as Activity & {
                type: 'hotcopper';
                thread: { thread: string; views: number };
                title: string;
            };

            const threadId = hotcopperActivity.thread.thread;

            if (!threadMap.has(threadId)) {
                threadMap.set(threadId, []);
            }
            threadMap.get(threadId)!.push(activity);
        });

        // Calculate stats for each thread
        const stats: ThreadStats[] = [];
        threadMap.forEach((threadActivities, threadId) => {
            // Get the first activity to extract thread info
            const firstActivity = threadActivities[0] as Activity & {
                type: 'hotcopper';
                thread: { thread: string; views: number };
                title: string;
            };

            stats.push({
                threadId,
                title: firstActivity.title || 'Untitled Thread',
                activityId: firstActivity.id!,
                views: firstActivity.thread?.views || 0,
                replies: threadActivities.length,
                sampleActivity: firstActivity
            });
        });

        // Sort by views (descending) and take top 5
        return stats.sort((a, b) => b.views - a.views).slice(0, 5);
    }, [activities]);

    if (threadStats.length === 0) {
        return (
            <div className="text-center text-gray-500 py-8 text-sm">
                No thread data available
            </div>
        );
    }

    function handleClick(activityId: string) {
        setIsRightPanelExpanded(true);
        router.push(
            `/investors/activities/${activityId}${searchParams.size > 0 ? `?${searchParams.toString()}` : ''}`
        );
    }

    return (
        <div className="overflow-hidden">
            <div className="overflow-x-auto">
                <table className="min-w-full">
                    <thead>
                        <tr className="border-b border-gray-200 bg-gray-100">
                            <th className="text-left text-xs font-normal text-gray-500 tracking-wider py-3 px-2">
                                Title
                            </th>
                            <th className="text-left text-xs font-normal text-gray-500 tracking-wider py-3 px-4">
                                Views
                            </th>
                            <th className="text-right text-xs font-normal text-gray-500 tracking-wider py-3 px-2">
                                Replies
                            </th>
                        </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                        {threadStats.map((stats) => (
                            <tr
                                key={stats.threadId}
                                className="hover:bg-gray-50"
                                role="button"
                                onClick={() => handleClick(stats.activityId)}>
                                <td className="py-3 px-4 max-w-xs">
                                    <div className="line-clamp-2 break-words text-xs font-medium text-gray-900">
                                        {stats.title}
                                    </div>
                                </td>

                                <td className="py-3 px-4">
                                    <div className="line-clamp-2 break-words text-xs font-medium text-gray-900 truncate">
                                        {stats.views.toLocaleString()}
                                    </div>
                                </td>
                                <td className="py-3 px-4">
                                    <div className="line-clamp-2 break-words text-xs font-medium text-gray-900 truncate">
                                        {stats.replies}
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
}
