import {
    ActivityFormData,
    ActivitySource,
    Format,
    MediaFormData,
    NewsSource
} from '@quarterback/types';
import { Author } from '@quarterback/types';
import { createDate } from '@/util/date';

export type FormAction =
    | { type: 'SET_FORMAT'; payload: Format }
    | {
          type: 'SET_SOURCE';
          payload: { sourceType: ActivitySource; sourceData?: NewsSource };
      }
    | { type: 'SET_AUTHOR'; payload: Author }
    | { type: 'UPDATE_FIELD'; payload: { field: string; value: any } }
    | { type: 'RESET' };

export type ActivityFormState = Omit<ActivityFormData, 'format' | 'source'> & {
    format: Format | undefined;
    source: ActivitySource | undefined;
};

export const initialFormState: ActivityFormState = {
    format: undefined,
    source: undefined,
    body: '',
    title: '',
    url: '',
    image: '',
    posted: createDate().toISOString(),
    likes: '0'
} as ActivityFormState;

export function formReducer(
    state: ActivityFormState,
    action: FormAction
): ActivityFormData {
    switch (action.type) {
        case 'SET_FORMAT': {
            const format = action.payload;
            let source: ActivitySource | undefined = undefined;
            if (format === 'announcement') {
                source = 'asx-announcement';
            }

            if (['call', 'meeting', 'presentation', 'event', 'other'].includes(format)) {
                source = format as ActivitySource;
            }

            if (['chatter', 'broadcast'].includes(format)) {
                source = 'advfn';
            }

            return {
                ...initialFormState,
                format,
                source,
                posted: createDate().toISOString()
            } as ActivityFormState;
        }

        case 'SET_SOURCE': {
            const { sourceType, sourceData } = action.payload;

            const newState = {
                ...state,
                source: sourceType,
                author: undefined
            } as ActivityFormState;

            if (sourceType === 'media' && sourceData) {
                (newState as MediaFormData).newsSource = sourceData;
            }

            return newState;
        }

        case 'SET_AUTHOR': {
            const author = action.payload;

            return {
                ...state,
                author
            } as ActivityFormState;
        }

        case 'UPDATE_FIELD':
            return {
                ...state,
                [action.payload.field]: action.payload.value
            } as ActivityFormState;

        case 'RESET':
            return {
                ...initialFormState,
                posted: createDate().toISOString()
            };

        default:
            return state;
    }
}
