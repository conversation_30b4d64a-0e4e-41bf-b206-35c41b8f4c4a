import { Sentiment } from '@quarterback/types';
import {
    DiscreteSentiment,
    discreteSentiment,
    SENTIMENT_TEXT_COLORS,
    sentimentScore
} from '@/util/sentiment';
import React, { useMemo } from 'react';
import classNames from 'classnames';
import { FaceFrownIcon, FaceSmileIcon } from '@heroicons/react/24/outline';

export default function SentimentIndicator({
    sentiment,
    showIcon = false,
    className = ''
}: {
    sentiment: Sentiment;
    showIcon?: boolean;
    className?: string;
}) {
    const score = sentimentScore(sentiment);
    const discrete = discreteSentiment(score);

    return (
        <div
            style={{ color: SENTIMENT_TEXT_COLORS[discrete] }}
            className={classNames(
                'inline-flex gap-x-1 items-center rounded-md px-2 py-1 text-slate-700',
                className
            )}>
            {showIcon &&
                (score < 0 ? (
                    <FaceFrownIcon className="size-4" />
                ) : (
                    <FaceSmileIcon className="size-4" />
                ))}
            <span>{score.toFixed(2)}</span>
        </div>
    );
}
