import useAuthorUpdateMutation from '@/api/hooks/mutations/useAuthorUpdateMutation';
import { TextField } from '@/components/forms/fields';
import { Author } from '@quarterback/types';
import { FormEvent, useState } from 'react';

interface FormData {
    name: string;
    notes: string;
    url: string;
    image: string;
    followers: number | null;
    following: number | null;
}

export default function EditPeople({ author }: { author: Author }) {
    const { updateAuthor, isLoading, error } = useAuthorUpdateMutation();
    const [formData, setFormData] = useState<FormData>(() => {
        return {
            name: author.name || '',
            url: author.url || '',
            image: author.image || '',
            notes: author?.notes || '',
            followers: author?.followers ?? null,
            following: author?.following ?? null
        };
    });

    const [submitError, setSubmitError] = useState<string | null>(null);
    const [submitSuccess, setSubmitSuccess] = useState<boolean>(false);

    const updateField = (field: keyof FormData, value: string | number | null) => {
        setFormData({
            ...formData,
            [field]: value
        });
    };

    const handleSubmit = async (event: FormEvent) => {
        event.preventDefault();
        setSubmitError(null);
        setSubmitSuccess(false);

        try {
            await updateAuthor({
                key: author.key,
                name: formData.name,
                url: formData.url || null,
                image: formData.image || null,
                notes: formData.notes || null,
                followers: formData.followers,
                following: formData.following
            });
            setSubmitSuccess(true);
        } catch (err) {
            setSubmitError('Failed to update author information. Please try again.');
        }
    };

    const resetForm = () => {
        if (!author) return;
        setFormData({
            name: author.name || '',
            url: author.url || '',
            image: author.image || '',
            notes: author.notes || '',
            followers: author.followers ?? null,
            following: author.following ?? null
        });
        setSubmitError(null);
        setSubmitSuccess(false);
    };

    return (
        <form
            onSubmit={handleSubmit}
            className="space-y-4"
            id="edit-people"
            onReset={resetForm}>
            {submitError && (
                <div className="rounded-md bg-red-50 p-4">
                    <div className="text-sm text-red-700">{submitError}</div>
                </div>
            )}
            {submitSuccess && (
                <div className="rounded-md bg-green-50 p-4">
                    <div className="text-sm text-green-700">
                        Author updated successfully!
                    </div>
                </div>
            )}

            <TextField
                label="Name"
                value={formData.name}
                onChange={(value) => updateField('name', value)}
                placeholder="Enter author name"
                required
            />

            <TextField
                label="Profile URL"
                value={formData.url}
                onChange={(value) => updateField('url', value)}
                placeholder="https://example.com/profile"
                type="url"
            />

            <TextField
                label="Image URL"
                value={formData.image}
                onChange={(value) => updateField('image', value)}
                placeholder="https://example.com/image.jpg"
                type="url"
            />

            <TextField
                label="Followers"
                value={formData.followers?.toString() || ''}
                onChange={(value) =>
                    updateField('followers', value ? parseInt(value) : null)
                }
                placeholder="Number of followers"
                type="number"
            />

            <TextField
                label="Following"
                value={formData.following?.toString() || ''}
                onChange={(value) =>
                    updateField('following', value ? parseInt(value) : null)
                }
                placeholder="Number of following"
                type="number"
            />
            <TextField
                label="Notes"
                value={formData.notes}
                onChange={(value) => updateField('notes', value)}
                placeholder="Add notes"
            />
        </form>
    );
}
