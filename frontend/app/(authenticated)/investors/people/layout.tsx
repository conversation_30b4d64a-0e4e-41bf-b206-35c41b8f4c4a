'use client';

import { useMemo, useState } from 'react';
import PeopleStickyHeader, { SORT_ORDER, SortOrderValue } from './StickyHeader';
import Checkbox from '@/components/ui/Checkbox';
import PeopleGroup from './ActivityGroup';
import { startOfDay, sub } from 'date-fns';
import { DateRange } from 'react-day-picker';
import { useSearchParams } from 'next/navigation';
import PeopleLayoutContext, { ActivityByPeople } from './PeopleLayoutContext';
import classNames from 'classnames';
import { useInvestorsLayout } from '../InvestorsLayoutContext';
import usePeopleStats from '@/api/hooks/usePeopleStats';

export default function PeopleLayout({ children }: { children: React.ReactNode }) {
    const searchParams = useSearchParams();
    const { isRightPanelExpanded } = useInvestorsLayout();

    const [selectedFilter, setSelectedFilter] = useState<string | undefined>(undefined);
    const [selectedSort, setSelectedSort] = useState<string | undefined>(undefined);
    const [sortOrder, setSortOrder] = useState<SortOrderValue>(SORT_ORDER.ASC);
    const [selectedPeople, setSelectedPeople] = useState<string[]>([]);
    const [range, setRange] = useState<DateRange>({
        from: startOfDay(
            searchParams.has('from')
                ? new Date(searchParams.get('from')!)
                : sub(new Date(), { months: 1 })
        ),
        to: startOfDay(
            searchParams.has('to') ? new Date(searchParams.get('to')!) : new Date()
        )
    });

    const {
        activitiesByPeople,
        timeSeriesByDate,
        getAveragePriceChangeOfAuthor,
        authorChatters
    } = usePeopleStats(range);

    const sortedData = useMemo(() => {
        return [...activitiesByPeople].sort((a, b) => {
            let aVal: number | string;
            let bVal: number | string;

            switch (selectedSort) {
                case 'lastActivityDate':
                    aVal = new Date(a.latestDate).getTime();
                    bVal = new Date(b.latestDate).getTime();
                    break;
                case 'mostActivities':
                    aVal = a.activitiesCount;
                    bVal = b.activitiesCount;
                    break;
                case 'avgSentiment':
                    aVal = a.averageSentiment;
                    bVal = b.averageSentiment;
                    break;

                default:
                    aVal = a.author.key.toLowerCase();
                    bVal = b.author.key.toLowerCase();
                    break;
            }
            const sortDirection = sortOrder === 'desc' ? -1 : 1;

            return aVal < bVal ? -1 * sortDirection : aVal > bVal ? 1 * sortDirection : 0;
        });
    }, [activitiesByPeople, selectedSort, sortOrder]);

    const handleCheckboxClick = (e: React.ChangeEvent<HTMLInputElement>) => {
        e.stopPropagation();
        if (e.target.checked) {
            setSelectedPeople(
                activitiesByPeople
                    .filter((a: ActivityByPeople) => a.author.key)
                    .map((a: ActivityByPeople) => a.author.key)
            );
        } else {
            setSelectedPeople([]);
        }
    };

    return (
        <PeopleLayoutContext.Provider
            value={{
                range,
                setRange,
                selectedSort,
                setSelectedSort,
                sortOrder,
                setSortOrder,
                selectedPeople,
                setSelectedPeople,
                activitiesByPeople,
                timeSeriesByDate,
                getAveragePriceChangeOfAuthor,
                authorChatters
            }}>
            <PeopleStickyHeader
                selectedSort={selectedSort}
                setSelectedSort={setSelectedSort}
                sortOrder={sortOrder}
                setSortOrder={setSortOrder}
                selectedPeople={selectedPeople}
                range={range}
                setRange={setRange}
                activities={sortedData}
                selectedFilter={selectedFilter}
                setSelectedFilter={setSelectedFilter}
            />
            <div className="flex justify-between items-center">
                <table className="w-full bg-white table-auto relative border-collapse ">
                    <thead>
                        <tr className="text-sm text-gray-900">
                            <th className="w-10">
                                <Checkbox
                                    checked={
                                        activitiesByPeople.length ===
                                        selectedPeople.length
                                    }
                                    onClick={(e: React.MouseEvent) => e.stopPropagation()}
                                    onChange={handleCheckboxClick}
                                />
                            </th>
                            <th className=" px-4 py-2 text-left">Person</th>
                            <th className="border-x border-qb-gray-115 px-4 py-2 text-right font-semibold">
                                Average Sentiment
                            </th>
                            <th className="text-sm border-x border-qb-gray-115 px-4 py-2 text-right">
                                Activities
                            </th>
                            <th className="border-x border-qb-gray-115 px-4 py-2 text-right">
                                Last Activities
                            </th>
                            <th className="border-x border-qb-gray-115 px-6 py-2 text-right">
                                Stock Held
                            </th>
                        </tr>
                    </thead>
                    <tbody className="bg-white">
                        {sortedData.map(
                            ({
                                author,
                                group,
                                averageSentiment,
                                latestDate,
                                lastActivity,
                                activitiesCount
                            }) => {
                                return (
                                    <PeopleGroup
                                        key={author?.key}
                                        author={author}
                                        averageSentiment={averageSentiment}
                                        latestDate={latestDate}
                                        group={group}
                                        lastActivity={lastActivity}
                                        activitiesCount={activitiesCount}
                                    />
                                );
                            }
                        )}
                    </tbody>
                </table>

                <div
                    className={classNames(
                        { 'w-[34rem] z-10': isRightPanelExpanded },
                        'top-0 bottom-0 right-0 overflow-y-scroll fixed bg-transparent mt-[60px]'
                    )}>
                    {children}
                </div>
            </div>
        </PeopleLayoutContext.Provider>
    );
}
