'use client';

import { useState } from 'react';
import InvestorsLayoutContext from './InvestorsLayoutContext';

export default function InvestorsLayout({ children }: { children: React.ReactNode }) {
    const [isRightPanelExpanded, setIsRightPanelExpanded] = useState(false);

    return (
        <InvestorsLayoutContext.Provider
            value={{
                isRightPanelExpanded,
                setIsRightPanelExpanded
            }}>
            {children}
        </InvestorsLayoutContext.Provider>
    );
}
