import { usePutMutation } from '@/api/hooks/util/useMutation';
import { Author } from '@quarterback/types';
import { useCallback, useMemo } from 'react';
import { useSWRConfig } from 'swr';

interface UpdateAuthorParams {
    key: string;
    name: string;
    url?: string | null;
    image?: string | null;
    followers?: number | null;
    following?: number | null;
    notes?: string | null;
}

export default function useAuthorUpdateMutation() {
    const { mutate, cache } = useSWRConfig();

    const invalidate = useCallback(async () => {
        const keys = Array.from(cache.keys());

        for (const key of keys) {
            if (
                [
                    /^\/v1\/authors.*$/,
                    /^\$inf\$\/v1\/authors.*$/,
                    /^\/admin\/authors.*$/,
                    /^\$inf\$\/admin\/authors.*$/
                ].some((it) => it.test(key))
            ) {
                await mutate(key);
            }
        }
    }, [mutate, cache]);

    const mutation = usePutMutation<Author, UpdateAuthorParams>('/v1/authors');

    return useMemo(
        () => ({
            async updateAuthor(params: UpdateAuthorParams) {
                const result = await mutation.trigger(params);
                await invalidate();
                return result;
            },
            isLoading: mutation.isMutating,
            error: mutation.error
        }),
        [invalidate, mutation]
    );
}
