import { ActivityByPeople } from '@/app/(authenticated)/investors/people/PeopleLayoutContext';
import { useOrganisation } from '@/components/OrganisationProvider';
import { author as getAuthor } from '@/util/author';
import hasField from '@/util/hasField';
import { sentimentScore } from '@/util/sentiment';
import { Activity, Author } from '@quarterback/types';
import { groupBy } from '@quarterback/util';
import { formatInTimeZone } from 'date-fns-tz';
import { useMemo } from 'react';
import { DateRange } from 'react-day-picker';
import useActivities from './useActivities';
import useTimeSeries from './useTimeSeries';

export default function usePeopleStats(range: DateRange) {
    const organisation = useOrganisation();

    const { data: activities = [], isLoading: activitiesLoading } = useActivities(
        organisation.selected?.organisation,
        organisation.selected?.entity,
        range.from!,
        range.to!
    );

    const { data: timeSeriesData, isLoading: timeSeriesLoading } = useTimeSeries(
        organisation.selected?.entity,
        range.from!,
        range.to!
    );

    const timeSeries = useMemo(() => {
        if (!timeSeriesData) return [];
        return timeSeriesData.values;
    }, [timeSeriesData]);

    const timeSeriesByDate = useMemo(() => {
        if (!timeSeries || timeSeries.length === 0) return {};

        return groupBy(timeSeries, (quote) =>
            formatInTimeZone(new Date(quote.datetime), 'Australia/Sydney', 'yyyy-MM-dd')
        );
    }, [timeSeries]);

    const activitiesByPeople = useMemo(() => {
        if (!activities || activities.length === 0) return [];

        // Group activities by author
        const authorMap = new Map<string, Activity[]>();

        activities.forEach((activity) => {
            if (activity?.author?.key) {
                if (!authorMap.has(activity.author.key)) {
                    authorMap.set(activity.author.key, []);
                }
                authorMap.get(activity.author.key)!.push(activity);
            }
        });

        // Convert to ActivityByPeople format
        const result: ActivityByPeople[] = [];

        authorMap.forEach((authorActivities, authorKey) => {
            // Get author info from first activity
            const firstActivity = authorActivities[0];
            const author: Author = {
                key: authorKey,
                name: firstActivity.author?.name || authorKey,
                userId: firstActivity.author?.userId || '',
                url: firstActivity.author?.url || '',
                image: firstActivity.author?.image || '',
                followers: firstActivity.author?.followers || null,
                following: firstActivity.author?.following || null,
                notes: firstActivity.author?.notes || ''
            };

            // Group activities by date
            const activitiesByDate = groupBy(authorActivities, (activity) =>
                formatInTimeZone(activity.posted!, 'Australia/Sydney', 'yyyy-MM-dd')
            );

            const group = Object.entries(activitiesByDate).map(
                ([date, dateActivities]) => ({
                    name: date,
                    datetime: new Date(date),
                    activities: dateActivities
                })
            );

            // Calculate average sentiment
            const sentimentActivities = authorActivities.filter(hasField('sentiment'));
            let averageSentiment = 0;
            if (sentimentActivities.length > 0) {
                const totalSentiment = sentimentActivities.reduce(
                    (sum, activity) => sum + sentimentScore(activity.sentiment),
                    0
                );
                averageSentiment = totalSentiment / sentimentActivities.length;
            }

            // Get latest activity date
            const latestDate = new Date(
                Math.max(...authorActivities.map((a) => new Date(a.posted!).getTime()))
            );
            const lastActivity = authorActivities.find(
                (a) => new Date(a.posted!).getTime() === latestDate.getTime()
            )!;

            result.push({
                author,
                group,
                activitiesCount: authorActivities.length,
                averageSentiment,
                latestDate,
                lastActivity
            });
        });

        return result;
    }, [activities]);

    const getAveragePriceChangeOfAuthor = useMemo(() => {
        return (authorKey: string): number => {
            // Find activities for this author
            const authorActivities = activities.filter(
                (activity) => activity.author?.key === authorKey
            );

            if (authorActivities.length === 0) return 0;

            // Calculate average price change based on activity dates
            let totalPriceChange = 0;
            let validChanges = 0;

            authorActivities.forEach((activity) => {
                const activityDate = formatInTimeZone(
                    activity.posted!,
                    'Australia/Sydney',
                    'yyyy-MM-dd'
                );
                const dayQuotes = timeSeriesByDate[activityDate];

                if (dayQuotes && dayQuotes.length > 0) {
                    const quote = dayQuotes[0];
                    // Calculate percentage change from open to close
                    if (quote.open && quote.close) {
                        const changePercent =
                            ((quote.close - quote.open) / quote.open) * 100;
                        totalPriceChange += changePercent;
                        validChanges++;
                    }
                }
            });

            return validChanges > 0 ? totalPriceChange / validChanges : 0;
        };
    }, [activities, timeSeriesByDate]);

    const authorChatters = useMemo(() => {
        return (authorKey: string): Activity[] => {
            return activities.filter(
                (activity) =>
                    activity.author?.key === authorKey && activity.format === 'chatter'
            );
        };
    }, [activities]);

    return {
        activitiesByPeople,
        timeSeriesByDate,
        getAveragePriceChangeOfAuthor,
        authorChatters,
        isLoading: activitiesLoading || timeSeriesLoading
    };
}
