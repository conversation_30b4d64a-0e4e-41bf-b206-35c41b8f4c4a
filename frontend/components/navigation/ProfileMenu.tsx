import React, { useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { signOut } from 'supertokens-auth-react/recipe/session';
import { Menu, MenuButton, MenuItem, MenuItems, Transition } from '@headlessui/react';
import Link from 'next/link';
import classNames from 'classnames';
import { useUser } from '@/api/hooks/useUser';
import {
    ArrowRightStartOnRectangleIcon,
    Cog6ToothIcon,
    QuestionMarkCircleIcon,
    UserIcon
} from '@heroicons/react/24/outline';

export default function ProfileMenu({ children }: React.PropsWithChildren) {
    const router = useRouter();
    const { data: user } = useUser();
    const { first_name: firstName, last_name: lastName } = user?.metadata ?? {};

    async function handleSignOut() {
        await signOut();
        localStorage.removeItem('selected-organisation');
        router.push('/login');
    }
    const email = user?.emails?.[0];

    const signedInUser = useMemo(() => {
        if (firstName && lastName) {
            return `${firstName} ${lastName}`;
        } else if (email) {
            return email;
        }
    }, [firstName, lastName, email]);

    return (
        <Menu as="div" className="relative">
            <MenuButton className="-m-1.5 flex items-center p-1.5">{children}</MenuButton>

            <Transition
                enter="transition ease-out duration-100"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95">
                <MenuItems
                    anchor={{ to: 'top start', gap: 12 }}
                    className="absolute right-0 z-10 -mt-1 w-72 origin-bottom-left rounded-md bg-white shadow-lg ring-1 ring-gray-900/5 focus:outline-none ">
                    <div className="px-3 py-4 border-b border-gray-100">
                        <span className="text-gray-600">Signed in as</span>
                        <div>{signedInUser}</div>
                    </div>

                    <div className="py-1">
                        <MenuItem>
                            {({ focus }) => (
                                <Link
                                    href="/company-settings"
                                    className={classNames(
                                        focus ? 'bg-gray-50' : '',
                                        'px-3 py-1 text-sm leading-6 text-gray-900 flex items-center'
                                    )}>
                                    <Cog6ToothIcon className="h-4 w-4 mr-2" />
                                    Company Settings
                                </Link>
                            )}
                        </MenuItem>
                    </div>
                    <div className="py-1">
                        <MenuItem>
                            {({ focus }) => (
                                <Link
                                    href="/account"
                                    className={classNames(
                                        focus ? 'bg-gray-50' : '',
                                        'px-3 py-1 text-sm leading-6 text-gray-900 flex items-center'
                                    )}>
                                    <UserIcon className="h-4 w-4 mr-2" />
                                    Account Settings
                                </Link>
                            )}
                        </MenuItem>
                    </div>
                    <div className="py-1">
                        <MenuItem>
                            {({ focus }) => (
                                <button
                                    onClick={() => {}}
                                    className={classNames(
                                        focus ? 'bg-gray-50' : '',
                                        'w-full text-left px-3 py-2 text-sm leading-6 text-gray-900 flex items-center'
                                    )}>
                                    <QuestionMarkCircleIcon className="h-4 w-4 mr-2" />
                                    Support
                                </button>
                            )}
                        </MenuItem>
                    </div>
                    <div>
                        <MenuItem>
                            {({ focus }) => (
                                <button
                                    onClick={handleSignOut}
                                    className={classNames(
                                        focus ? 'bg-gray-50' : '',
                                        'w-full text-left px-3 py-3 text-sm leading-6 text-gray-900 border-t border-gray-100 flex items-center'
                                    )}>
                                    <ArrowRightStartOnRectangleIcon className="h-4 w-4 mr-2" />
                                    Sign out
                                </button>
                            )}
                        </MenuItem>
                    </div>
                </MenuItems>
            </Transition>
        </Menu>
    );
}
