import React from 'react';
import Initials, { InitialsProps } from '../Initials';
import classNames from 'classnames';
import Link from 'next/link';

interface ContentCardProps {
    title: React.ReactNode;
    titleHref?: string;
    className?: string;
    childrenClassName?: string;
    initialBadge?: InitialsProps;
    children: React.ReactNode;
    loading?: boolean;
}

function ContentCard(props: ContentCardProps) {
    const {
        title,
        titleHref,
        className = '',
        childrenClassName = '',
        initialBadge = {},
        children,
        loading = false
    } = props;

    const renderTitle = () => {
        const titleContent = (
            <span className="bg-qb-gray-50 flex gap-2 p-2 rounded-md hover:underline text-qb-black-100 hover:no-underline">
                <Initials
                    name={initialBadge.name || (typeof title === 'string' ? title : '')}
                    color={initialBadge.color}
                    hide={initialBadge.hide}
                />
                <span className="text-sm font-medium">{title}</span>
            </span>
        );

        if (typeof title === 'string' && titleHref) {
            return <Link href={titleHref} className='no-underline hover:no-underline'>{titleContent}</Link>;
        }

        return titleContent;
    };
    return (
        <div className={classNames('bg-white border rounded-lg p-4', className)}>
            <div className="flex gap-x-2 divide-gray-200 items-center">
                {typeof title === 'string' ? (
                    renderTitle()
                ) : (
                    <div className="flex flex-1 flex-col">
                        <span className="text-sm font-medium">{title}</span>
                    </div>
                )}
            </div>
            <div className="w-full mt-2 border-b border-gray-200"></div>
            {loading ? (
                <div className="w-full max-w-sm p-4 bg-white">
                    <div className="animate-pulse flex space-x-4">
                        <div className="flex-1 space-y-4 py-1">
                            <div className="h-4 bg-gray-200"></div>
                            <div className="space-y-2">
                                <div className="h-4 bg-gray-200"></div>
                                <div className="h-4 bg-gray-200  w-5/6"></div>
                            </div>
                        </div>
                    </div>
                </div>
            ) : (
                <div className={classNames('mt-4', childrenClassName)}>{children}</div>
            )}
        </div>
    );
}

export default ContentCard;
