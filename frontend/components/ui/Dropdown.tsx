import {
    Listbox,
    ListboxButton,
    ListboxOption,
    ListboxOptions,
    Transition
} from '@headlessui/react';
import classNames from 'classnames';
import React, { Fragment } from 'react';

export type DropdownOption = {
    name: React.ReactNode;
    value: string;
};

type DropdownProps = {
    title?: string;
    icon?: React.ComponentType<{ className: string | undefined }>;
    options: DropdownOption[];
    selected?: string | number | undefined;
    onChange: (value: string) => void;
    classes?: string;
    buttonClass?: string;
    placeholder?: React.ReactNode;
    borderDashed?: boolean;
    styled?: boolean;
};

export default function Dropdown({
    title,
    icon: Icon,
    options,
    selected,
    onChange,
    classes,
    buttonClass,
    placeholder,
    borderDashed,
    styled
}: DropdownProps) {
    const selectedItem = selected ? options.find((a) => a.value === selected) : undefined;

    return (
        <div className={classNames('inline-flex items-center')}>
            <Listbox value={selected} onChange={onChange}>
                {({ open }) => (
                    <div className={classNames('relative inline-block text-left')}>
                        <div className="inline-flex items-center text-sm text-gray-700">
                            {title && (
                                <div
                                    className={classNames(
                                        { 'border-dashed': borderDashed },
                                        {
                                            'border-transparent bg-transparent':
                                                styled && selectedItem
                                        },
                                        classes,
                                        'flex items-center gap-1 px-3 py-1.5 border border-r-0 border-qb-gray-105 rounded-l-md bg-qb-gray-50'
                                    )}>
                                    {Icon && <Icon className="w-4 h-4 text-gray-400" />}
                                    <span>{title}</span>
                                </div>
                            )}
                            <div className="relative">
                                <ListboxButton
                                    className={classNames(
                                        buttonClass,
                                        { 'border-dashed': borderDashed },
                                        {
                                            'border-transparent bg-transparent':
                                                styled && selectedItem
                                        },
                                        { 'rounded-l-md border-l-none': !title },
                                        'rounded-r-md flex items-center bg-qb-gray-50 border border-qb-gray-105 text-sm text-gray-600 py-1.5 px-3'
                                    )}>
                                    {selectedItem?.name ? (
                                        <span className="font-medium text-gray-700">
                                            {selectedItem?.name}
                                        </span>
                                    ) : (
                                        <span className="font-medium text-gray-600">
                                            {placeholder ?? 'Select'}
                                        </span>
                                    )}
                                </ListboxButton>

                                <Transition
                                    as={Fragment}
                                    show={open}
                                    enter="transition ease-out duration-100"
                                    enterFrom="opacity-0 scale-95"
                                    enterTo="opacity-100 scale-100"
                                    leave="transition ease-in duration-75"
                                    leaveFrom="opacity-100 scale-100"
                                    leaveTo="opacity-0 scale-95">
                                    <ListboxOptions className="absolute z-10 mt-1 w-48 p-1 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none text-sm overflow-hidden ">
                                        {options.map((option, index) => (
                                            <ListboxOption
                                                key={option.value}
                                                value={option.value}>
                                                {({ focus, selected }) => (
                                                    <div
                                                        className={classNames(
                                                            focus || selected
                                                                ? 'bg-qb-gray-20 border-white'
                                                                : '',
                                                            {
                                                                'border-qb-gray-20': focus
                                                            },
                                                            'text-gray-900 cursor-default select-none px-4 py-2 rounded-md  border-y border-white '
                                                        )}>
                                                        {option.name}
                                                    </div>
                                                )}
                                            </ListboxOption>
                                        ))}
                                    </ListboxOptions>
                                </Transition>
                            </div>
                        </div>
                    </div>
                )}
            </Listbox>
        </div>
    );
}
