import React, { useMemo } from 'react';
import { Activity } from '@quarterback/types';
import { ParentSize } from '@visx/responsive';
import { Group } from '@visx/group';
import Pie, { PieArcDatum } from '@visx/shape/lib/shapes/Pie';
import { DISCLOSURE_COLORS, DISCLOSURE_LABELS } from '@/components/HotcopperStatus';
import classNames from 'classnames';

interface DisclosureData {
    label: string;
    count: number;
    color: string;
    disclosure: string;
}

function mapColor(color: string) {
    const mappedColors = {
        'text-qb-blue-300': '#4285F4',
        'text-qb-purple-300': '#D0BCFF',
        'text-qb-gray-150': '#F2F2F2'
    };

    return mappedColors[color as keyof typeof mappedColors] || color;
}

export const HotcopperDisclosurePieChart = function HotcopperDisclosurePieChart({
    data,
    width: parentWidth = 200,
    height: parentHeight = 200
}: {
    data: Array<DisclosureData>;
    width: number;
    height: number;
}) {
    const width = parentWidth;
    const height = parentHeight;

    const radius = Math.min(width, height) / 2;
    const centerY = height / 2;
    const centerX = width / 2;

    const getValue = (d: DisclosureData) => d.count;
    const getColor = (arc: PieArcDatum<DisclosureData>) => mapColor(arc.data.color);

    const filteredData = data.filter((d) => d.count > 0);

    if (filteredData.length === 0) {
        return (
            <svg width={width} height={height}>
                <Group top={centerY} left={centerX}>
                    <circle r={radius} fill="#f3f4f6" />
                    <text textAnchor="middle" dy="0.33em" fontSize={14} fill="#6b7280">
                        No data
                    </text>
                </Group>
            </svg>
        );
    }

    return (
        <svg width={width} height={height}>
            <Group top={centerY} left={centerX}>
                <Pie
                    data={filteredData}
                    pieValue={getValue}
                    outerRadius={radius}
                    innerRadius={0}
                    cornerRadius={2}
                    padAngle={0.01}>
                    {(pie) => (
                        <>
                            {pie.arcs.map((arc, index) => (
                                <path
                                    key={`arc-${index}`}
                                    d={pie.path(arc) || ''}
                                    fill={getColor(arc)}
                                />
                            ))}
                        </>
                    )}
                </Pie>
            </Group>
        </svg>
    );
};

interface Props {
    activities: Array<Activity>;
}

export default function HotcopperDisclosurePie({ activities }: Props) {
    const hotcopperActivities = useMemo(
        () => activities.filter((activity) => activity.type === 'hotcopper'),
        [activities]
    );

    const [heldCount, notHeldCount, undisclosedCount] = useMemo(() => {
        let held = 0;
        let notHeld = 0;
        let undisclosed = 0;

        hotcopperActivities.forEach((activity) => {
            if (activity.type === 'hotcopper') {
                const disclosure = activity.hotcopper?.disclosure;
                switch (disclosure) {
                    case 'HELD':
                        held++;
                        break;
                    case 'NOT_HELD':
                        notHeld++;
                        break;
                    case 'UNDISCLOSED':
                    default:
                        undisclosed++;
                        break;
                }
            }
        });

        return [held, notHeld, undisclosed];
    }, [hotcopperActivities]);

    const data: Array<DisclosureData> = useMemo(
        () => [
            {
                label: DISCLOSURE_LABELS.HELD,
                count: heldCount,
                color: DISCLOSURE_COLORS.HELD[0],
                disclosure: 'HELD'
            },
            {
                label: DISCLOSURE_LABELS.NOT_HELD,
                count: notHeldCount,
                color: DISCLOSURE_COLORS.NOT_HELD[0],
                disclosure: 'NOT_HELD'
            },
            {
                label: DISCLOSURE_LABELS.UNDISCLOSED,
                count: undisclosedCount,
                color: DISCLOSURE_COLORS.UNDISCLOSED[0],
                disclosure: 'UNDISCLOSED'
            }
        ],
        [heldCount, notHeldCount, undisclosedCount]
    );

    return (
        <>
            <div className="h-56 p-4 relative">
                <ParentSize>
                    {({ width, height }) => (
                        <HotcopperDisclosurePieChart
                            width={width}
                            height={height}
                            data={data}
                        />
                    )}
                </ParentSize>
            </div>
            <div className="flex flex-wrap gap-y-2 gap-x-4 px-4 pb-4">
                {data
                    .filter((item) => item.count > 0)
                    .map((item) => (
                        <div
                            key={item.label}
                            className="text-sm flex items-center gap-x-2">
                            <div
                                className={classNames(
                                    'w-3 h-3 rounded-full flex-shrink-0'
                                )}
                                style={{ backgroundColor: mapColor(item.color) }}
                            />
                            <span className="truncate">{item.label}</span>
                            {/* <span className="text-gray-500 ml-auto">{item.count}</span> */}
                        </div>
                    ))}
            </div>
        </>
    );
}
