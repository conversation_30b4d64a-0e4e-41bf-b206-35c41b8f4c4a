import React, { useMemo } from 'react';
import { Activity } from '@quarterback/types';
import { ParentSize } from '@visx/responsive';
import { Group } from '@visx/group';
import Pie, { PieArcDatum } from '@visx/shape/lib/shapes/Pie';

interface SentimentData {
    label: string;
    count: number;
    color: string;
    sentiment: string;
}

const SENTIMENT_COLORS = {
    BUY: '#AAD89B', // green-300 (positive)
    SELL: '#F19292', // red-300 (negative)
    HOLD: '#FBF197', // yellow-300 (neutral/holding)
    NONE: '#F2F2F2' // slate-200 (no sentiment)
};

const SENTIMENT_LABELS = {
    BUY: 'Buy',
    SELL: 'Sell',
    HOLD: 'Hold',
    NONE: 'None'
};

export const HotcopperSentimentPieChart = function HotcopperSentimentPieChart({
    data,
    width: parentWidth = 200,
    height: parentHeight = 200
}: {
    data: Array<SentimentData>;
    width: number;
    height: number;
}) {
    const width = parentWidth;
    const height = parentHeight;

    const radius = Math.min(width, height) / 2;
    const centerY = height / 2;
    const centerX = width / 2;

    const getValue = (d: SentimentData) => d.count;
    const getColor = (arc: PieArcDatum<SentimentData>) => arc.data.color;

    const filteredData = data.filter((d) => d.count > 0);

    if (filteredData.length === 0) {
        return (
            <svg width={width} height={height}>
                <Group top={centerY} left={centerX}>
                    <circle r={radius} fill="#f3f4f6" />
                    <text textAnchor="middle" dy="0.33em" fontSize={14} fill="#6b7280">
                        No data
                    </text>
                </Group>
            </svg>
        );
    }

    return (
        <svg width={width} height={height}>
            <Group top={centerY} left={centerX}>
                <Pie
                    data={filteredData}
                    pieValue={getValue}
                    outerRadius={radius}
                    innerRadius={0}
                    cornerRadius={2}
                    padAngle={0.01}>
                    {(pie) => (
                        <>
                            {pie.arcs.map((arc, index) => (
                                <path
                                    key={`arc-${index}`}
                                    d={pie.path(arc) || ''}
                                    fill={getColor(arc)}
                                />
                            ))}
                        </>
                    )}
                </Pie>
            </Group>
        </svg>
    );
};

interface Props {
    activities: Array<Activity>;
}

export default function HotcopperSentimentPie({ activities }: Props) {
    const hotcopperActivities = useMemo(
        () => activities.filter((activity) => activity.type === 'hotcopper'),
        [activities]
    );

    const [buyCount, sellCount, holdCount, noneCount] = useMemo(() => {
        let buy = 0;
        let sell = 0;
        let hold = 0;
        let none = 0;

        hotcopperActivities.forEach((activity) => {
            if (activity.type === 'hotcopper') {
                const sentiment = activity.hotcopper?.sentiment;
                switch (sentiment) {
                    case 'BUY':
                        buy++;
                        break;
                    case 'SELL':
                        sell++;
                        break;
                    case 'HOLD':
                        hold++;
                        break;
                    case 'NONE':
                    default:
                        none++;
                        break;
                }
            }
        });

        return [buy, sell, hold, none];
    }, [hotcopperActivities]);

    const data: Array<SentimentData> = useMemo(
        () => [
            {
                label: SENTIMENT_LABELS.BUY,
                count: buyCount,
                color: SENTIMENT_COLORS.BUY,
                sentiment: 'BUY'
            },
            {
                label: SENTIMENT_LABELS.SELL,
                count: sellCount,
                color: SENTIMENT_COLORS.SELL,
                sentiment: 'SELL'
            },
            {
                label: SENTIMENT_LABELS.HOLD,
                count: holdCount,
                color: SENTIMENT_COLORS.HOLD,
                sentiment: 'HOLD'
            },
            {
                label: SENTIMENT_LABELS.NONE,
                count: noneCount,
                color: SENTIMENT_COLORS.NONE,
                sentiment: 'NONE'
            }
        ],
        [buyCount, sellCount, holdCount, noneCount]
    );

    return (
        <>
            <div className="h-56 p-4 relative">
                <ParentSize>
                    {({ width, height }) => (
                        <HotcopperSentimentPieChart
                            width={width}
                            height={height}
                            data={data}
                        />
                    )}
                </ParentSize>
            </div>
            <div className="flex flex-wrap gap-y-2 gap-x-4 px-4 pb-4">
                {data
                    .filter((item) => item.count > 0)
                    .map((item) => (
                        <div
                            key={item.label}
                            className="text-sm flex items-center gap-x-2">
                            <div
                                className="w-3 h-3 rounded-full flex-shrink-0"
                                style={{ backgroundColor: item.color }}
                            />
                            <span className="truncate">{item.label}</span>
                            {/* <span className="text-gray-500 ml-auto">{item.count}</span> */}
                        </div>
                    ))}
            </div>
        </>
    );
}
