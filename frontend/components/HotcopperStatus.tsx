import { HotCopperPost } from '@quarterback/types';
import classNames from 'classnames';

export const DISCLOSURE_COLORS = {
    HELD: ['text-qb-blue-300', 'bg-qb-blue-100'],
    NOT_HELD: ['text-qb-purple-300', 'bg-qb-purple-50'],
    UNDISCLOSED: ['text-qb-gray-150', 'bg-qb-gray-100']
};

export const DISCLOSURE_LABELS = {
    HELD: 'Held',
    NOT_HELD: 'Not Held',
    UNDISCLOSED: 'Undisclosed'
};

export default function HotCopperStatus({ activity }: { activity: HotCopperPost }) {
    return (
        <span
            className={classNames(
                'inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset ',
                ...DISCLOSURE_COLORS[activity.hotcopper?.disclosure ?? 'UNDISCLOSED']
            )}>
            {DISCLOSURE_LABELS[activity.hotcopper?.disclosure ?? 'UNDISCLOSED']}
        </span>
    );
}
