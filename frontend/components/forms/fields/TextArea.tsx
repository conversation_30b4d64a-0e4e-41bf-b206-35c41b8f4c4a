import { TextAreaProps } from '@quarterback/types';
import classNames from 'classnames';

export function TextArea({
    label,
    value,
    onChange,
    onBlur,
    rows = 4,
    placeholder,
    error,
    textSize = 'base',
    required = false,
    styled = false,
    className = ''
}: TextAreaProps) {
    const textSizeStyles = {
        sm: 'text-sm/6 placeholder-qb-gray-150',
        base: 'text-base placeholder-qb-gray-150',
        lg: 'text-[1.25rem] leading-[1.688rem] placeholder-qb-gray-150'
    };

    const baseStyles = classNames(
        styled
            ? 'block w-full border-0 focus:border-0 focus:ring-0 focus:outline-none'
            : '',
        !styled
            ? 'block w-full rounded-md sm:text-sm placeholder-qb-gray-150 border-gray-300 focus:border-indigo-500 focus:ring-indigo-500'
            : '',
        error && !styled
            ? 'border-red-300 text-red-900 focus:border-red-500 focus:ring-red-500'
            : '',
        error && styled ? 'text-red-900' : '',
        className
    );

    return (
        <div>
            {label ? (
                <label className="block text-sm font-medium text-qb-gray-150 mb-1">
                    {label}
                    {required && <span className="text-red-500">*</span>}
                </label>
            ) : null}
            <textarea
                className={classNames(baseStyles, textSizeStyles[textSize])}
                rows={rows}
                value={value}
                onChange={(e) => {
                    onChange(e.target.value);
                }}
                onBlur={onBlur}
                placeholder={placeholder}
                required={required}
            />
            {error && <p className="mt-2 text-sm text-red-600">{error}</p>}
        </div>
    );
}
