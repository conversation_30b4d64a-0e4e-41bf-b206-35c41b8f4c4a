import { TextFieldProps } from '@quarterback/types';
import classNames from 'classnames';

export function TextField({
    label,
    value,
    onChange,
    onBlur,
    placeholder,
    min,
    max,
    step,
    error,
    readOnly = false,
    required = false,
    type = 'text',
    textSize = 'base',
    styled = false,
    className = ''
}: TextFieldProps) {
    const textSizeStyles = {
        sm: 'text-sm placeholder-qb-gray-150',
        base: 'text-base placeholder-qb-gray-150',
        lg: 'text-lg placeholder-qb-gray-150'
    };

    const baseStyles = classNames(
        styled
            ? 'block w-full border-0 focus:border-0 focus:ring-0 focus:outline-none'
            : '',
        !styled
            ? 'block w-full rounded-md sm:text-sm placeholder-qb-gray-150 border-gray-300 focus:border-indigo-500 focus:ring-indigo-500'
            : '',
        error && !styled
            ? 'border-red-300 text-red-900 focus:border-red-500 focus:ring-red-500'
            : '',
        error && styled ? 'text-red-900' : '',
        className
    );

    return (
        <div>
            {label ? (
                <label className="block text-sm font-medium text-qb-gray-150 mb-1">
                    {label}
                    {required && <span className="text-indigo-500">*</span>}
                </label>
            ) : null}
            <input
                type={type}
                readOnly={readOnly}
                className={classNames(baseStyles, textSizeStyles[textSize])}
                value={value}
                min={min}
                max={max}
                step={step}
                onChange={(e) => {
                    onChange(e.target.value);
                }}
                onBlur={onBlur}
                placeholder={placeholder}
                required={required}
            />
            {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
        </div>
    );
}
