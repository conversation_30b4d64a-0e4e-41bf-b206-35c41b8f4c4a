import { Router } from 'express';
import db from '../../db/index.js';
import { author } from '../../db/schema/activities.js';
import { and, ilike, sql } from 'drizzle-orm';
import z from 'zod';
import { Author } from '@quarterback/types';
import { getDomain } from '@quarterback/util';

const router: Router = Router();

const QuerySchema = z.object({
    limit: z.coerce.number().min(1).max(100).default(10),
    offset: z.coerce.number().min(0).default(0),
    search: z.string().optional(),
    source: z.string().optional()
});

const CreateAuthorSchema = z.object({
    userId: z.string().min(1, 'User ID is required'),
    name: z.string().min(1, 'Name is required'),
    source: z.string().min(1, 'Source is required'),
    url: z
        .string()
        .transform((val) => (val === '' || val === null ? undefined : val))
        .pipe(z.string().url('Must be a valid URL').nullable().optional()),
    notes: z.string().nullable().optional(),
    image: z.string().nullable().optional(),
    followers: z.number().nullable().optional(),
    following: z.number().nullable().optional()
});

const UpdateAuthorSchema = z.object({
    key: z.string().min(1, 'Key is required'),
    name: z.string().min(1, 'Name is required'),
    url: z.string().url('Must be a valid URL').nullable().optional(),
    image: z.string().url('Image must be a valid URL').nullable().optional(),
    notes: z.string().nullable().optional(),
    followers: z.number().nullable().optional(),
    following: z.number().nullable().optional()
});

router.get('/', async (request, response) => {
    try {
        const { limit, offset, search, source } = QuerySchema.parse(request.query);

        // Build the where conditions
        let whereConditions = [];

        // Add search condition if provided
        if (search) {
            whereConditions.push(ilike(author.name, `%${search}%`));
        }

        // Add source filter if provided
        if (source) {
            whereConditions.push(ilike(author.key, `${source}%`));
        }

        // Combine conditions
        const whereCondition =
            whereConditions.length > 0 ? and(...whereConditions) : undefined;

        // Get total count for pagination info
        const countResult = await db
            .select({
                count: sql<number>`count(*)`
            })
            .from(author)
            .where(whereCondition);

        const totalCount = countResult[0]?.count || 0;

        // Execute the main query with pagination
        const authors = await db
            .select({
                key: author.key,
                userId: author.userId,
                name: author.name,
                image: author.image,
                url: author.url,
                notes: author.notes,
                followers: author.followers,
                following: author.following
            })
            .from(author)
            .where(whereCondition)
            .limit(limit)
            .offset(offset)
            .orderBy(author.name);

        return response.status(200).json({
            authors,
            pagination: {
                total: totalCount,
                limit,
                offset
            }
        });
    } catch (error) {
        console.error('Error fetching authors:', error);
        return response.status(500).json({ error: 'Failed to fetch authors' });
    }
});

// POST endpoint to create a new author
router.post('/', async (request, response) => {
    try {
        const authorData = CreateAuthorSchema.parse(request.body);

        // Combine key with userId to create the final key
        const authorKey = `${authorData.source}__${authorData.userId}`;

        // Check if author already exists
        const existingAuthor = await db
            .select({ key: author.key })
            .from(author)
            .where(sql`${author.key} = ${authorKey}`)
            .limit(1);

        if (existingAuthor.length > 0) {
            return response.status(409).json({
                error: 'Author already exists',
                key: authorKey
            });
        }

        // Insert the new author
        await db.insert(author).values({
            key: authorKey,
            userId: authorData.userId,
            name: authorData.name,
            url: authorData.url,
            image: authorData.image,
            followers: authorData.followers,
            following: authorData.following,
            notes: authorData.notes
        });

        // Return the created author
        const createdAuthor: Author = {
            key: authorKey,
            userId: authorData.userId,
            name: authorData.name,
            url: authorData.url,
            image: authorData.image,
            followers: authorData.followers,
            following: authorData.following,
            notes: authorData.notes
        };

        return response.status(201).json(createdAuthor);
    } catch (error) {
        console.error('Error creating author:', error);
        if (error instanceof z.ZodError) {
            return response.status(400).json({
                error: 'Invalid author data',
                details: error.errors
            });
        }
        return response.status(500).json({ error: 'Failed to create author' });
    }
});

router.put('/', async (request, response) => {
    try {
        const authorData = UpdateAuthorSchema.parse(request.body);

        const existingAuthor = await db
            .select()
            .from(author)
            .where(sql`${author.key} = ${authorData.key}`)
            .limit(1);

        if (existingAuthor.length === 0) {
            return response.status(404).json({
                error: 'Author not found',
                key: authorData.key
            });
        }

        await db
            .update(author)
            .set({
                name: authorData.name,
                url: authorData.url,
                image: authorData.image,
                followers: authorData.followers,
                following: authorData.following,
                notes: authorData.notes
            })
            .where(sql`${author.key} = ${authorData.key}`);

        // Return the updated author
        const updatedAuthor = await db
            .select()
            .from(author)
            .where(sql`${author.key} = ${authorData.key}`)
            .limit(1);

        return response.status(200).json(updatedAuthor[0]);
    } catch (error) {
        console.error('Error updating author:', JSON.stringify(error));
        if (error instanceof z.ZodError) {
            return response.status(400).json({
                error: 'Invalid author data',
                details: error.errors
            });
        }
        return response.status(500).json({ error: 'Failed to update author' });
    }
});

export default router;
