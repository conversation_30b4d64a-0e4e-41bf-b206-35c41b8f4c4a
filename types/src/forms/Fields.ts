interface BaseFieldProps {
    label?: string;
    error?: string;
    required?: boolean;
    className?: string;
    styled?: boolean;
}

export interface TextFieldProps extends BaseFieldProps {
    type?: 'text' | 'number' | 'email' | 'password' | 'url';
    textSize?: 'sm' | 'base' | 'lg';
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
    readOnly?: boolean;
    min?: number;
    max?: number;
    step?: number;
    onBlur?: () => void;
}

export interface SelectFieldProps extends BaseFieldProps {
    value: string | undefined;
    onChange: (value: string) => void;
    options: Array<{ value: string; label: string }>;
}

export interface TextAreaProps extends BaseFieldProps {
    value: string;
    textSize?: 'sm' | 'base' | 'lg';
    onChange: (value: string) => void;
    rows?: number;
    placeholder?: string;
    onBlur?: () => void;
}

export interface DateTimeFieldProps extends BaseFieldProps {
    value: Date | null;
    onChange: (value: Date) => void;
    onBlur?: () => void;
    timezone?: string;
    onTimezoneChange?: (timezone: string) => void;
}

export interface CheckboxFieldProps extends BaseFieldProps {
    value: boolean;
    onChange: (value: boolean) => void;
}

export interface RadioFieldProps extends BaseFieldProps {
    value: string;
    onChange: (value: string) => void;
    options: Array<{ value: string; label: string }>;
}

export interface FileFieldProps extends BaseFieldProps {
    value: File | null;
    onChange: (value: File) => void;
}
